'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Bug, Loader2, Send } from 'lucide-react';
import { createTestNotification, createTestDispatcherForStudent } from '../actions/debug-actions';
import { toast } from 'sonner';

/**
 * Botão de debug para criar notificações de teste
 * Só aparece em ambiente de desenvolvimento
 */
export function DebugNotificationButton() {
  const [isLoading, setIsLoading] = useState(false);
  const [isDispatcherLoading, setIsDispatcherLoading] = useState(false);

  // ID do estudante para teste
  const STUDENT_TEST_ID = '8aecebc9-4145-4a21-ae45-4a714e08f7bf';

  // Só renderizar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const handleCreateTestNotification = async () => {
    setIsLoading(true);
    
    try {
      const result = await createTestNotification();
      
      if (result.success) {
        toast.success(result.message || 'Notificação de teste criada!', {
          description: 'Verifique o painel de notificações para ver a nova notificação.',
          duration: 5000
        });
      } else {
        toast.error('Erro ao criar notificação', {
          description: result.error || 'Erro desconhecido',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Erro ao criar notificação de teste:', error);
      toast.error('Erro inesperado', {
        description: 'Ocorreu um erro inesperado ao criar a notificação de teste.',
        duration: 5000
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTestDispatcher = async () => {
    setIsDispatcherLoading(true);

    try {
      const result = await createTestDispatcherForStudent(STUDENT_TEST_ID);

      if (result.success) {
        toast.success(result.message || 'Dispatcher de teste criado!', {
          description: `Notificação enviada para o estudante ${STUDENT_TEST_ID}`,
          duration: 5000
        });
      } else {
        toast.error('Erro ao criar dispatcher', {
          description: result.error || 'Erro desconhecido',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Erro ao criar dispatcher de teste:', error);
      toast.error('Erro inesperado', {
        description: 'Ocorreu um erro inesperado ao criar o dispatcher de teste.',
        duration: 5000
      });
    } finally {
      setIsDispatcherLoading(false);
    }
  };

  return (
    <div className="flex gap-2">
      <Button
        onClick={handleCreateTestNotification}
        disabled={isLoading}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100 dark:bg-orange-950/20 dark:border-orange-800 dark:text-orange-400 dark:hover:bg-orange-900/30"
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Bug className="h-4 w-4" />
        )}
        {isLoading ? 'Criando...' : 'Debug: Criar Notificação'}
      </Button>

      <Button
        onClick={handleCreateTestDispatcher}
        disabled={isDispatcherLoading}
        variant="outline"
        size="sm"
        className="flex items-center gap-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 dark:bg-blue-950/20 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/30"
      >
        {isDispatcherLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Send className="h-4 w-4" />
        )}
        {isDispatcherLoading ? 'Enviando...' : 'Debug: Dispatcher Teste'}
      </Button>
    </div>
  );
}
