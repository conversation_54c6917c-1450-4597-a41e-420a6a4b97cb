/**
 * Arquivo de debug para testar templates específicos de notificação
 * Testa especificamente os templates de lembrete de pagamento e aula
 */

import { exemploLembretePagemento, exemploLembreteAula } from '../examples/email-usage-example';
import { NotificationDispatcher } from '../channels/notification-dispatcher';

/**
 * Testa especificamente o template de lembrete de pagamento
 */
export async function debugPaymentReminderTemplate() {
  console.log('🔧 [DEBUG] Testando Payment Reminder Template...\n');
  
  try {
    const dispatcher = new NotificationDispatcher();

    // Dados de teste para o template de pagamento
    const testData = {
      tenantId: 'debug-tenant-001',
      userId: 'debug-user-001',
      studentName: '<PERSON> (DEBUG)',
      amount: 199.90,
      dueDate: '2024-02-20',
      planName: 'Plano Premium - Jiu-Jitsu + Muay Thai',
      paymentMethod: 'PIX',
      invoiceUrl: 'https://app.academia.com/invoice/debug-123',
      channels: ['email', 'in_app'] as const
    };

    console.log('📧 Enviando notificação de lembrete de pagamento...');
    console.log('Dados do teste:', JSON.stringify(testData, null, 2));

    const resultado = await dispatcher.sendPaymentReminder(testData);

    console.log('\n📊 Resultado do envio:');
    console.log('✅ Sucesso:', resultado.success);
    
    if (resultado.success) {
      console.log('🆔 ID da notificação:', resultado.notificationId);
      
      // Verificar resultados por canal
      console.log('\n📡 Resultados por canal:');
      Object.entries(resultado.channelResults).forEach(([canal, resultado]) => {
        if (resultado.success) {
          console.log(`  ✅ ${canal}: Enviado (ID: ${resultado.messageId})`);
        } else {
          console.log(`  ❌ ${canal}: Erro - ${resultado.error}`);
        }
      });
    } else {
      console.log('❌ Erros:', resultado.errors);
    }

  } catch (error) {
    console.error('🚨 Erro no teste do Payment Reminder Template:', error);
  }

  console.log('\n' + '='.repeat(60) + '\n');
}

/**
 * Testa especificamente o template de lembrete de aula
 */
export async function debugClassReminderTemplate() {
  console.log('🔧 [DEBUG] Testando Class Reminder Template...\n');
  
  try {
    const dispatcher = new NotificationDispatcher();

    // Dados de teste para o template de aula
    const testData = {
      tenantId: 'debug-tenant-001',
      userId: 'debug-user-002',
      studentName: 'Maria Santos (DEBUG)',
      className: 'Muay Thai - Avançado',
      instructorName: 'Professor Carlos Silva',
      classDate: '2024-02-15',
      classTime: '19:30',
      location: 'Sala 2 - Tatame Principal',
      duration: 90,
      maxStudents: 20,
      currentEnrollments: 15,
      classDetailsUrl: 'https://app.academia.com/class/debug-456',
      cancelUrl: 'https://app.academia.com/class/debug-456/cancel',
      rescheduleUrl: 'https://app.academia.com/class/debug-456/reschedule',
      reminderType: 'today' as const,
      requiresEquipment: ['luvas', 'caneleiras'],
      specialInstructions: 'Trazer toalha e garrafa de água. Aula focada em técnicas de clinch.',
      channels: ['email', 'in_app'] as const
    };

    console.log('🥋 Enviando notificação de lembrete de aula...');
    console.log('Dados do teste:', JSON.stringify(testData, null, 2));

    const resultado = await dispatcher.sendClassReminder(testData);

    console.log('\n📊 Resultado do envio:');
    console.log('✅ Sucesso:', resultado.success);
    
    if (resultado.success) {
      console.log('🆔 ID da notificação:', resultado.notificationId);
      
      // Verificar resultados por canal
      console.log('\n📡 Resultados por canal:');
      Object.entries(resultado.channelResults).forEach(([canal, resultado]) => {
        if (resultado.success) {
          console.log(`  ✅ ${canal}: Enviado (ID: ${resultado.messageId})`);
        } else {
          console.log(`  ❌ ${canal}: Erro - ${resultado.error}`);
        }
      });
    } else {
      console.log('❌ Erros:', resultado.errors);
    }

  } catch (error) {
    console.error('🚨 Erro no teste do Class Reminder Template:', error);
  }

  console.log('\n' + '='.repeat(60) + '\n');
}

/**
 * Executa ambos os testes de template
 */
export async function debugBothTemplates() {
  console.log('🚀 [DEBUG] Iniciando testes dos templates de notificação...\n');
  console.log('📧 Payment Reminder Template: payment-reminder-template.tsx');
  console.log('🥋 Class Reminder Template: class-reminder-template.tsx\n');
  console.log('='.repeat(60) + '\n');

  // Teste 1: Payment Reminder Template
  await debugPaymentReminderTemplate();

  // Teste 2: Class Reminder Template  
  await debugClassReminderTemplate();

  console.log('🎉 [DEBUG] Testes concluídos!\n');
  console.log('💡 Dica: Verifique os logs acima para ver se os templates foram renderizados corretamente.');
  console.log('📧 Se configurado, os e-mails de teste foram enviados para os usuários debug.');
}

/**
 * Função de teste rápido usando os exemplos existentes
 */
export async function debugQuickTest() {
  console.log('⚡ [DEBUG] Teste rápido usando exemplos existentes...\n');

  console.log('📧 Testando Payment Reminder (exemplo existente)...');
  await exemploLembretePagemento();
  
  console.log('\n🥋 Testando Class Reminder (exemplo existente)...');
  await exemploLembreteAula();
  
  console.log('\n✅ Teste rápido concluído!');
}

// Para executar no console ou em desenvolvimento:
// import { debugBothTemplates } from '@/services/notifications/debug/test-notification-templates';
// debugBothTemplates();
