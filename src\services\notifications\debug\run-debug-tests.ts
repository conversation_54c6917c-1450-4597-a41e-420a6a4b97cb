#!/usr/bin/env tsx

/**
 * Script para executar testes de debug das notificações
 * Execute com: npx tsx src/services/notifications/debug/run-debug-tests.ts
 */

import { debugBothTemplates, debugPaymentReminderTemplate, debugClassReminderTemplate, debugQuickTest } from './test-notification-templates';

async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'both';

  console.log('🔧 Sistema de Debug - Templates de Notificação\n');

  switch (command) {
    case 'payment':
      console.log('Executando teste do Payment Reminder Template...\n');
      await debugPaymentReminderTemplate();
      break;
      
    case 'class':
      console.log('Executando teste do Class Reminder Template...\n');
      await debugClassReminderTemplate();
      break;
      
    case 'quick':
      console.log('Executando teste rápido...\n');
      await debugQuickTest();
      break;
      
    case 'both':
    default:
      console.log('Executando teste de ambos os templates...\n');
      await debugBothTemplates();
      break;
  }

  console.log('\n📋 Comandos disponíveis:');
  console.log('  npx tsx src/services/notifications/debug/run-debug-tests.ts both     # Testa ambos os templates');
  console.log('  npx tsx src/services/notifications/debug/run-debug-tests.ts payment  # Testa apenas payment reminder');
  console.log('  npx tsx src/services/notifications/debug/run-debug-tests.ts class    # Testa apenas class reminder');
  console.log('  npx tsx src/services/notifications/debug/run-debug-tests.ts quick    # Teste rápido com exemplos');
}

main().catch(console.error);
